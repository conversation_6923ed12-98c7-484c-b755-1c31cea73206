import {
  AssignUserToWorkItemRequest,
  StatsWorkItems,
  UpdateAutomatedStatusRequest,
  UpdateSiteRequest,
  UpdateWorkItemStatusRequest,
  WorkItem,
} from '@experience/commercial/statement-service/shared';
import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  Param,
  ParseUUIDPipe,
  Post,
  Put,
  UseInterceptors,
  ValidationPipe,
} from '@nestjs/common';
import { WorkItemInterceptor } from './work-item.interceptor';
import { WorkItemService } from './work-item.service';

@UseInterceptors(WorkItemInterceptor)
@Controller('work-items')
export class WorkItemController {
  constructor(private workItemService: WorkItemService) {}

  @Get()
  async findAllWorkItems(): Promise<WorkItem[]> {
    return this.workItemService.findAllWorkItems();
  }

  @Get('stats')
  async countWorkItems(): Promise<StatsWorkItems> {
    return this.workItemService.countWorkItems();
  }

  @Get(':workItemId')
  async findWorkItem(
    @Param('workItemId', ParseUUIDPipe) workItemId: string
  ): Promise<WorkItem> {
    return this.workItemService.findWorkItem(workItemId);
  }

  @Post(':workItemId/user')
  async assignUserToWorkItem(
    @Param('workItemId', ParseUUIDPipe) workItemId: string,
    @Body(ValidationPipe) request: AssignUserToWorkItemRequest
  ): Promise<void> {
    return this.workItemService.assignUserToWorkItem(workItemId, request);
  }

  @Delete(':workItemId/user')
  @HttpCode(204)
  async removeUserFromWorkItem(
    @Param('workItemId', ParseUUIDPipe) workItemId: string
  ): Promise<void> {
    return this.workItemService.removeUserFromWorkItem(workItemId);
  }

  @Put(':workItemId/automated')
  async updateAutomatedStatus(
    @Param('workItemId', ParseUUIDPipe) workItemId: string,
    @Body(ValidationPipe) request: UpdateAutomatedStatusRequest
  ) {
    return this.workItemService.updateAutomatedStatus(workItemId, request);
  }

  @Put(':workItemId/status')
  async updateWorkItemStatus(
    @Param('workItemId', ParseUUIDPipe) workItemId: string,
    @Body(ValidationPipe) request: UpdateWorkItemStatusRequest
  ) {
    return this.workItemService.updateWorkItemStatus(workItemId, request);
  }

  @Put(':workItemId/statement')
  async updateStatementByWorkItemId(
    @Param('workItemId', ParseUUIDPipe) workItemId: string,
    @Body(ValidationPipe) request: UpdateSiteRequest
  ): Promise<void> {
    return this.workItemService.updateSiteConfigByWorkItemId(
      workItemId,
      request
    );
  }
}
