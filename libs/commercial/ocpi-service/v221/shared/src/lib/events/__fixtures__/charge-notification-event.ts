import { ChargeNotificationEvent } from '../charge-notification-event';

export const TEST_CHARGE_NOTIFICATION_EVENT: ChargeNotificationEvent = {
  event: 'Charge.Completed',
  eventID: 'bd024098-ef0b-433d-9cc2-1060a232bc4c',
  publishedAt: '2025-02-05T13:29:42Z',
  payload: {
    authorisation: {
      claimedChargeId: 850814311,
    },
    chargingStation: {
      id: 'PG-12345',
      doorId: 'A',
    },
    charge: {
      id: 113418058,
      unpluggedAt: '2025-02-05T13:29:38Z',
    },
  },
};
