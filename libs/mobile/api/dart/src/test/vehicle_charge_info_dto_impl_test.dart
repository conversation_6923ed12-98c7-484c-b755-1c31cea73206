//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

import 'package:openapi/api.dart';
import 'package:test/test.dart';

// tests for VehicleChargeInfoDtoImpl
void main() {
  // final instance = VehicleChargeInfoDtoImpl();

  group('test VehicleChargeInfoDtoImpl', () {
    // num expectedChargeByTargetPercent
    test('to test the property `expectedChargeByTargetPercent`', () async {
      // TODO
    });

    // num expectedChargeByTargetKWh
    test('to test the property `expectedChargeByTargetKWh`', () async {
      // TODO
    });

    // String fullChargeByTime
    test('to test the property `fullChargeByTime`', () async {
      // TODO
    });


  });

}
