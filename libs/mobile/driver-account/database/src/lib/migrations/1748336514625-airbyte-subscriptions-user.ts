import { MigrationInterface, QueryRunner } from 'typeorm';

export class AirbyteSubscriptionsUser1748336514625
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE ROLE subscriptions_api_airbyte WITH LOGIN;
        GRANT USAGE ON SCHEMA subscriptions TO subscriptions_api_airbyte;
        GRANT SELECT ON subscriptions.subscriptions to subscriptions_api_airbyte;
        GRANT SELECT ON subscriptions.actions to subscriptions_api_airbyte;
        GRANT SELECT ON subscriptions.plans to subscriptions_api_airbyte;
      `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        REVOKE SELECT ON subscriptions.plans FROM subscriptions_api_airbyte;
        REVOKE SELECT ON subscriptions.actions FROM subscriptions_api_airbyte;
        REVOKE SELECT ON subscriptions.subscriptions FROM subscriptions_api_airbyte;
        REVOKE USAGE ON SCHEMA subscriptions FROM subscriptions_api_airbyte;
        DROP ROLE IF EXISTS subscriptions_api_airbyte;
      `);
  }
}
