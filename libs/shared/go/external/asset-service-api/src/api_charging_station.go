/*
asset-service-api

An API for managing assets - Charging stations & Sub-assemblies (EVSEs/PCBs) ### NB: All date/time inputs are to be provided in ISO format and timezone UTC e.g. `2023-01-03T16:09:59.000Z`.

API version: 1.0
Contact: <EMAIL>
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package assetserviceapi

import (
	"bytes"
	"context"
	"io"
	"net/http"
	"net/url"
	"strings"
)

type ChargingStationAPI interface {

	/*
		CreateChargingStation Create a new charging station (only ownership information for now)

		Create a new charging station in Asset Service DB (associate existing device with ownership information)

		@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
		@return ChargingStationAPICreateChargingStationRequest
	*/
	CreateChargingStation(ctx context.Context) ChargingStationAPICreateChargingStationRequest

	// CreateChargingStationExecute executes the request
	CreateChargingStationExecute(r ChargingStationAPICreateChargingStationRequest) (*http.Response, error)

	/*
		DecommissionAChargingStation Decommission a charging station

		Decommissions a charging station for a given ppid

		@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
		@param ppid ppid from an existing unit
		@return ChargingStationAPIDecommissionAChargingStationRequest
	*/
	DecommissionAChargingStation(ctx context.Context, ppid string) ChargingStationAPIDecommissionAChargingStationRequest

	// DecommissionAChargingStationExecute executes the request
	DecommissionAChargingStationExecute(r ChargingStationAPIDecommissionAChargingStationRequest) (*http.Response, error)

	/*
		GetChargingStation Return asset details for a given charging station

		Lookup an asset summary based on the ppid (serial number) of the charging station

		@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
		@param ppid ppid from an existing unit
		@return ChargingStationAPIGetChargingStationRequest
	*/
	GetChargingStation(ctx context.Context, ppid string) ChargingStationAPIGetChargingStationRequest

	// GetChargingStationExecute executes the request
	//  @return ChargingStationSummary
	GetChargingStationExecute(r ChargingStationAPIGetChargingStationRequest) (*ChargingStationSummary, *http.Response, error)

	/*
		SearchChargingStations Search for the charging station

		Search for a charging station by EVSE mac address or serial number, results are keyed by the values of query parameter

		@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
		@return ChargingStationAPISearchChargingStationsRequest
	*/
	SearchChargingStations(ctx context.Context) ChargingStationAPISearchChargingStationsRequest

	// SearchChargingStationsExecute executes the request
	//  @return ChargingStationsSummary
	SearchChargingStationsExecute(r ChargingStationAPISearchChargingStationsRequest) (*ChargingStationsSummary, *http.Response, error)

	/*
		UpdateChargingStationOwnership Update a charging stations ownership in Asset Service DB

		Update a charging stations ownership in Asset Service DB

		@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
		@return ChargingStationAPIUpdateChargingStationOwnershipRequest
	*/
	UpdateChargingStationOwnership(ctx context.Context) ChargingStationAPIUpdateChargingStationOwnershipRequest

	// UpdateChargingStationOwnershipExecute executes the request
	UpdateChargingStationOwnershipExecute(r ChargingStationAPIUpdateChargingStationOwnershipRequest) (*http.Response, error)
}

// ChargingStationAPIService ChargingStationAPI service
type ChargingStationAPIService service

type ChargingStationAPICreateChargingStationRequest struct {
	ctx                              context.Context
	ApiService                       ChargingStationAPI
	createChargingStationRequestType *CreateChargingStationRequestType
}

func (r ChargingStationAPICreateChargingStationRequest) CreateChargingStationRequestType(createChargingStationRequestType CreateChargingStationRequestType) ChargingStationAPICreateChargingStationRequest {
	r.createChargingStationRequestType = &createChargingStationRequestType
	return r
}

func (r ChargingStationAPICreateChargingStationRequest) Execute() (*http.Response, error) {
	return r.ApiService.CreateChargingStationExecute(r)
}

/*
CreateChargingStation Create a new charging station (only ownership information for now)

Create a new charging station in Asset Service DB (associate existing device with ownership information)

	@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
	@return ChargingStationAPICreateChargingStationRequest
*/
func (a *ChargingStationAPIService) CreateChargingStation(ctx context.Context) ChargingStationAPICreateChargingStationRequest {
	return ChargingStationAPICreateChargingStationRequest{
		ApiService: a,
		ctx:        ctx,
	}
}

// Execute executes the request
func (a *ChargingStationAPIService) CreateChargingStationExecute(r ChargingStationAPICreateChargingStationRequest) (*http.Response, error) {
	var (
		localVarHTTPMethod = http.MethodPost
		localVarPostBody   interface{}
		formFiles          []formFile
	)

	localBasePath, err := a.client.cfg.ServerURLWithContext(r.ctx, "ChargingStationAPIService.CreateChargingStation")
	if err != nil {
		return nil, &GenericOpenAPIError{error: err.Error()}
	}

	localVarPath := localBasePath + "/charging-stations"

	localVarHeaderParams := make(map[string]string)
	localVarQueryParams := url.Values{}
	localVarFormParams := url.Values{}

	// to determine the Content-Type header
	localVarHTTPContentTypes := []string{"application/json"}

	// set Content-Type header
	localVarHTTPContentType := selectHeaderContentType(localVarHTTPContentTypes)
	if localVarHTTPContentType != "" {
		localVarHeaderParams["Content-Type"] = localVarHTTPContentType
	}

	// to determine the Accept header
	localVarHTTPHeaderAccepts := []string{}

	// set Accept header
	localVarHTTPHeaderAccept := selectHeaderAccept(localVarHTTPHeaderAccepts)
	if localVarHTTPHeaderAccept != "" {
		localVarHeaderParams["Accept"] = localVarHTTPHeaderAccept
	}
	// body params
	localVarPostBody = r.createChargingStationRequestType
	req, err := a.client.prepareRequest(r.ctx, localVarPath, localVarHTTPMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, formFiles)
	if err != nil {
		return nil, err
	}

	localVarHTTPResponse, err := a.client.callAPI(req)
	if err != nil || localVarHTTPResponse == nil {
		return localVarHTTPResponse, err
	}

	localVarBody, err := io.ReadAll(localVarHTTPResponse.Body)
	localVarHTTPResponse.Body.Close()
	localVarHTTPResponse.Body = io.NopCloser(bytes.NewBuffer(localVarBody))
	if err != nil {
		return localVarHTTPResponse, err
	}

	if localVarHTTPResponse.StatusCode >= 300 {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: localVarHTTPResponse.Status,
		}
		return localVarHTTPResponse, newErr
	}

	return localVarHTTPResponse, nil
}

type ChargingStationAPIDecommissionAChargingStationRequest struct {
	ctx                     context.Context
	ApiService              ChargingStationAPI
	ppid                    string
	decommissionRequestType *DecommissionRequestType
}

func (r ChargingStationAPIDecommissionAChargingStationRequest) DecommissionRequestType(decommissionRequestType DecommissionRequestType) ChargingStationAPIDecommissionAChargingStationRequest {
	r.decommissionRequestType = &decommissionRequestType
	return r
}

func (r ChargingStationAPIDecommissionAChargingStationRequest) Execute() (*http.Response, error) {
	return r.ApiService.DecommissionAChargingStationExecute(r)
}

/*
DecommissionAChargingStation Decommission a charging station

Decommissions a charging station for a given ppid

	@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
	@param ppid ppid from an existing unit
	@return ChargingStationAPIDecommissionAChargingStationRequest
*/
func (a *ChargingStationAPIService) DecommissionAChargingStation(ctx context.Context, ppid string) ChargingStationAPIDecommissionAChargingStationRequest {
	return ChargingStationAPIDecommissionAChargingStationRequest{
		ApiService: a,
		ctx:        ctx,
		ppid:       ppid,
	}
}

// Execute executes the request
func (a *ChargingStationAPIService) DecommissionAChargingStationExecute(r ChargingStationAPIDecommissionAChargingStationRequest) (*http.Response, error) {
	var (
		localVarHTTPMethod = http.MethodPost
		localVarPostBody   interface{}
		formFiles          []formFile
	)

	localBasePath, err := a.client.cfg.ServerURLWithContext(r.ctx, "ChargingStationAPIService.DecommissionAChargingStation")
	if err != nil {
		return nil, &GenericOpenAPIError{error: err.Error()}
	}

	localVarPath := localBasePath + "/charging-stations/{ppid}/decommission"
	localVarPath = strings.Replace(localVarPath, "{"+"ppid"+"}", url.PathEscape(parameterValueToString(r.ppid, "ppid")), -1)

	localVarHeaderParams := make(map[string]string)
	localVarQueryParams := url.Values{}
	localVarFormParams := url.Values{}

	// to determine the Content-Type header
	localVarHTTPContentTypes := []string{"application/json"}

	// set Content-Type header
	localVarHTTPContentType := selectHeaderContentType(localVarHTTPContentTypes)
	if localVarHTTPContentType != "" {
		localVarHeaderParams["Content-Type"] = localVarHTTPContentType
	}

	// to determine the Accept header
	localVarHTTPHeaderAccepts := []string{}

	// set Accept header
	localVarHTTPHeaderAccept := selectHeaderAccept(localVarHTTPHeaderAccepts)
	if localVarHTTPHeaderAccept != "" {
		localVarHeaderParams["Accept"] = localVarHTTPHeaderAccept
	}
	// body params
	localVarPostBody = r.decommissionRequestType
	req, err := a.client.prepareRequest(r.ctx, localVarPath, localVarHTTPMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, formFiles)
	if err != nil {
		return nil, err
	}

	localVarHTTPResponse, err := a.client.callAPI(req)
	if err != nil || localVarHTTPResponse == nil {
		return localVarHTTPResponse, err
	}

	localVarBody, err := io.ReadAll(localVarHTTPResponse.Body)
	localVarHTTPResponse.Body.Close()
	localVarHTTPResponse.Body = io.NopCloser(bytes.NewBuffer(localVarBody))
	if err != nil {
		return localVarHTTPResponse, err
	}

	if localVarHTTPResponse.StatusCode >= 300 {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: localVarHTTPResponse.Status,
		}
		return localVarHTTPResponse, newErr
	}

	return localVarHTTPResponse, nil
}

type ChargingStationAPIGetChargingStationRequest struct {
	ctx        context.Context
	ApiService ChargingStationAPI
	ppid       string
}

func (r ChargingStationAPIGetChargingStationRequest) Execute() (*ChargingStationSummary, *http.Response, error) {
	return r.ApiService.GetChargingStationExecute(r)
}

/*
GetChargingStation Return asset details for a given charging station

Lookup an asset summary based on the ppid (serial number) of the charging station

	@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
	@param ppid ppid from an existing unit
	@return ChargingStationAPIGetChargingStationRequest
*/
func (a *ChargingStationAPIService) GetChargingStation(ctx context.Context, ppid string) ChargingStationAPIGetChargingStationRequest {
	return ChargingStationAPIGetChargingStationRequest{
		ApiService: a,
		ctx:        ctx,
		ppid:       ppid,
	}
}

// Execute executes the request
//
//	@return ChargingStationSummary
func (a *ChargingStationAPIService) GetChargingStationExecute(r ChargingStationAPIGetChargingStationRequest) (*ChargingStationSummary, *http.Response, error) {
	var (
		localVarHTTPMethod  = http.MethodGet
		localVarPostBody    interface{}
		formFiles           []formFile
		localVarReturnValue *ChargingStationSummary
	)

	localBasePath, err := a.client.cfg.ServerURLWithContext(r.ctx, "ChargingStationAPIService.GetChargingStation")
	if err != nil {
		return localVarReturnValue, nil, &GenericOpenAPIError{error: err.Error()}
	}

	localVarPath := localBasePath + "/charging-stations/{ppid}"
	localVarPath = strings.Replace(localVarPath, "{"+"ppid"+"}", url.PathEscape(parameterValueToString(r.ppid, "ppid")), -1)

	localVarHeaderParams := make(map[string]string)
	localVarQueryParams := url.Values{}
	localVarFormParams := url.Values{}

	// to determine the Content-Type header
	localVarHTTPContentTypes := []string{}

	// set Content-Type header
	localVarHTTPContentType := selectHeaderContentType(localVarHTTPContentTypes)
	if localVarHTTPContentType != "" {
		localVarHeaderParams["Content-Type"] = localVarHTTPContentType
	}

	// to determine the Accept header
	localVarHTTPHeaderAccepts := []string{"application/json"}

	// set Accept header
	localVarHTTPHeaderAccept := selectHeaderAccept(localVarHTTPHeaderAccepts)
	if localVarHTTPHeaderAccept != "" {
		localVarHeaderParams["Accept"] = localVarHTTPHeaderAccept
	}
	req, err := a.client.prepareRequest(r.ctx, localVarPath, localVarHTTPMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, formFiles)
	if err != nil {
		return localVarReturnValue, nil, err
	}

	localVarHTTPResponse, err := a.client.callAPI(req)
	if err != nil || localVarHTTPResponse == nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	localVarBody, err := io.ReadAll(localVarHTTPResponse.Body)
	localVarHTTPResponse.Body.Close()
	localVarHTTPResponse.Body = io.NopCloser(bytes.NewBuffer(localVarBody))
	if err != nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	if localVarHTTPResponse.StatusCode >= 300 {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: localVarHTTPResponse.Status,
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	err = a.client.decode(&localVarReturnValue, localVarBody, localVarHTTPResponse.Header.Get("Content-Type"))
	if err != nil {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: err.Error(),
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	return localVarReturnValue, localVarHTTPResponse, nil
}

type ChargingStationAPISearchChargingStationsRequest struct {
	ctx          context.Context
	ApiService   ChargingStationAPI
	macAddress   *[]string
	serialNumber *[]string
}

// mac address(es) from an existing EVSE(s) (aka PCB)
func (r ChargingStationAPISearchChargingStationsRequest) MacAddress(macAddress []string) ChargingStationAPISearchChargingStationsRequest {
	r.macAddress = &macAddress
	return r
}

// serial number(s) from an existing EVSE(s) (aka PCB)
func (r ChargingStationAPISearchChargingStationsRequest) SerialNumber(serialNumber []string) ChargingStationAPISearchChargingStationsRequest {
	r.serialNumber = &serialNumber
	return r
}

func (r ChargingStationAPISearchChargingStationsRequest) Execute() (*ChargingStationsSummary, *http.Response, error) {
	return r.ApiService.SearchChargingStationsExecute(r)
}

/*
SearchChargingStations Search for the charging station

Search for a charging station by EVSE mac address or serial number, results are keyed by the values of query parameter

	@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
	@return ChargingStationAPISearchChargingStationsRequest
*/
func (a *ChargingStationAPIService) SearchChargingStations(ctx context.Context) ChargingStationAPISearchChargingStationsRequest {
	return ChargingStationAPISearchChargingStationsRequest{
		ApiService: a,
		ctx:        ctx,
	}
}

// Execute executes the request
//
//	@return ChargingStationsSummary
func (a *ChargingStationAPIService) SearchChargingStationsExecute(r ChargingStationAPISearchChargingStationsRequest) (*ChargingStationsSummary, *http.Response, error) {
	var (
		localVarHTTPMethod  = http.MethodGet
		localVarPostBody    interface{}
		formFiles           []formFile
		localVarReturnValue *ChargingStationsSummary
	)

	localBasePath, err := a.client.cfg.ServerURLWithContext(r.ctx, "ChargingStationAPIService.SearchChargingStations")
	if err != nil {
		return localVarReturnValue, nil, &GenericOpenAPIError{error: err.Error()}
	}

	localVarPath := localBasePath + "/charging-stations"

	localVarHeaderParams := make(map[string]string)
	localVarQueryParams := url.Values{}
	localVarFormParams := url.Values{}

	if r.macAddress != nil {
		parameterAddToHeaderOrQuery(localVarQueryParams, "macAddress", r.macAddress, "form", "csv")
	}
	if r.serialNumber != nil {
		parameterAddToHeaderOrQuery(localVarQueryParams, "serialNumber", r.serialNumber, "form", "csv")
	}
	// to determine the Content-Type header
	localVarHTTPContentTypes := []string{}

	// set Content-Type header
	localVarHTTPContentType := selectHeaderContentType(localVarHTTPContentTypes)
	if localVarHTTPContentType != "" {
		localVarHeaderParams["Content-Type"] = localVarHTTPContentType
	}

	// to determine the Accept header
	localVarHTTPHeaderAccepts := []string{"application/json"}

	// set Accept header
	localVarHTTPHeaderAccept := selectHeaderAccept(localVarHTTPHeaderAccepts)
	if localVarHTTPHeaderAccept != "" {
		localVarHeaderParams["Accept"] = localVarHTTPHeaderAccept
	}
	req, err := a.client.prepareRequest(r.ctx, localVarPath, localVarHTTPMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, formFiles)
	if err != nil {
		return localVarReturnValue, nil, err
	}

	localVarHTTPResponse, err := a.client.callAPI(req)
	if err != nil || localVarHTTPResponse == nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	localVarBody, err := io.ReadAll(localVarHTTPResponse.Body)
	localVarHTTPResponse.Body.Close()
	localVarHTTPResponse.Body = io.NopCloser(bytes.NewBuffer(localVarBody))
	if err != nil {
		return localVarReturnValue, localVarHTTPResponse, err
	}

	if localVarHTTPResponse.StatusCode >= 300 {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: localVarHTTPResponse.Status,
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	err = a.client.decode(&localVarReturnValue, localVarBody, localVarHTTPResponse.Header.Get("Content-Type"))
	if err != nil {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: err.Error(),
		}
		return localVarReturnValue, localVarHTTPResponse, newErr
	}

	return localVarReturnValue, localVarHTTPResponse, nil
}

type ChargingStationAPIUpdateChargingStationOwnershipRequest struct {
	ctx                        context.Context
	ApiService                 ChargingStationAPI
	changeOwnershipRequestType *ChangeOwnershipRequestType
}

func (r ChargingStationAPIUpdateChargingStationOwnershipRequest) ChangeOwnershipRequestType(changeOwnershipRequestType ChangeOwnershipRequestType) ChargingStationAPIUpdateChargingStationOwnershipRequest {
	r.changeOwnershipRequestType = &changeOwnershipRequestType
	return r
}

func (r ChargingStationAPIUpdateChargingStationOwnershipRequest) Execute() (*http.Response, error) {
	return r.ApiService.UpdateChargingStationOwnershipExecute(r)
}

/*
UpdateChargingStationOwnership Update a charging stations ownership in Asset Service DB

Update a charging stations ownership in Asset Service DB

	@param ctx context.Context - for authentication, logging, cancellation, deadlines, tracing, etc. Passed from http.Request or context.Background().
	@return ChargingStationAPIUpdateChargingStationOwnershipRequest
*/
func (a *ChargingStationAPIService) UpdateChargingStationOwnership(ctx context.Context) ChargingStationAPIUpdateChargingStationOwnershipRequest {
	return ChargingStationAPIUpdateChargingStationOwnershipRequest{
		ApiService: a,
		ctx:        ctx,
	}
}

// Execute executes the request
func (a *ChargingStationAPIService) UpdateChargingStationOwnershipExecute(r ChargingStationAPIUpdateChargingStationOwnershipRequest) (*http.Response, error) {
	var (
		localVarHTTPMethod = http.MethodPost
		localVarPostBody   interface{}
		formFiles          []formFile
	)

	localBasePath, err := a.client.cfg.ServerURLWithContext(r.ctx, "ChargingStationAPIService.UpdateChargingStationOwnership")
	if err != nil {
		return nil, &GenericOpenAPIError{error: err.Error()}
	}

	localVarPath := localBasePath + "/charging-stations/ownership"

	localVarHeaderParams := make(map[string]string)
	localVarQueryParams := url.Values{}
	localVarFormParams := url.Values{}

	// to determine the Content-Type header
	localVarHTTPContentTypes := []string{"application/json"}

	// set Content-Type header
	localVarHTTPContentType := selectHeaderContentType(localVarHTTPContentTypes)
	if localVarHTTPContentType != "" {
		localVarHeaderParams["Content-Type"] = localVarHTTPContentType
	}

	// to determine the Accept header
	localVarHTTPHeaderAccepts := []string{}

	// set Accept header
	localVarHTTPHeaderAccept := selectHeaderAccept(localVarHTTPHeaderAccepts)
	if localVarHTTPHeaderAccept != "" {
		localVarHeaderParams["Accept"] = localVarHTTPHeaderAccept
	}
	// body params
	localVarPostBody = r.changeOwnershipRequestType
	req, err := a.client.prepareRequest(r.ctx, localVarPath, localVarHTTPMethod, localVarPostBody, localVarHeaderParams, localVarQueryParams, localVarFormParams, formFiles)
	if err != nil {
		return nil, err
	}

	localVarHTTPResponse, err := a.client.callAPI(req)
	if err != nil || localVarHTTPResponse == nil {
		return localVarHTTPResponse, err
	}

	localVarBody, err := io.ReadAll(localVarHTTPResponse.Body)
	localVarHTTPResponse.Body.Close()
	localVarHTTPResponse.Body = io.NopCloser(bytes.NewBuffer(localVarBody))
	if err != nil {
		return localVarHTTPResponse, err
	}

	if localVarHTTPResponse.StatusCode >= 300 {
		newErr := &GenericOpenAPIError{
			body:  localVarBody,
			error: localVarHTTPResponse.Status,
		}
		return localVarHTTPResponse, newErr
	}

	return localVarHTTPResponse, nil
}
