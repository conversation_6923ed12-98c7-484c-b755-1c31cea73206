import { render, screen } from '@testing-library/react';
import PageHeader from './page-header';

const PAGE_HEADER_HEADING = 'Header heading';
const PAGE_HEADER_SUBHEADING = 'Header sub-heading';
const PAGE_HEADER_ACTION = <button>Action button</button>;

describe('PageHeader', () => {
  it('should render successfully', () => {
    const { baseElement } = render(
      <PageHeader
        heading={PAGE_HEADER_HEADING}
        subHeading={PAGE_HEADER_SUBHEADING}
        action={PAGE_HEADER_ACTION}
      />
    );

    expect(baseElement).toBeTruthy();
  });

  it('should render a heading', () => {
    render(<PageHeader heading={PAGE_HEADER_HEADING} />);

    expect(screen.getByText(PAGE_HEADER_HEADING)).toBeInTheDocument();
  });

  it('should render a sub-heading', () => {
    render(
      <PageHeader
        heading={PAGE_HEADER_HEADING}
        subHeading={PAGE_HEADER_SUBHEADING}
      />
    );

    expect(screen.getByText(PAGE_HEADER_SUBHEADING)).toBeInTheDocument();
  });

  it('should render an action button', () => {
    render(
      <PageHeader heading={PAGE_HEADER_HEADING} action={PAGE_HEADER_ACTION} />
    );

    expect(screen.getByText('Action button')).toBeInTheDocument();
  });

  it('should match snapshot', () => {
    const { baseElement } = render(
      <PageHeader
        heading={PAGE_HEADER_HEADING}
        subHeading={PAGE_HEADER_SUBHEADING}
        action={PAGE_HEADER_ACTION}
      />
    );

    expect(baseElement).toMatchSnapshot();
  });

  it('should match snapshot with no action', () => {
    const { baseElement } = render(
      <PageHeader
        heading={PAGE_HEADER_HEADING}
        subHeading={PAGE_HEADER_SUBHEADING}
        action={undefined}
      />
    );

    expect(baseElement).toMatchSnapshot();
  });
});
