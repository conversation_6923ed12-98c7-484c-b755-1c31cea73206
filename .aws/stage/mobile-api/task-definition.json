{"containerDefinitions": [{"cpu": 0, "environment": [{"name": "AWS_SES_CONFIGURATION_SET_NAME", "value": "monitoring"}, {"name": "ENVIRONMENT", "value": "stage"}, {"name": "EXPENSES_REPORT_QUEUE_URL", "value": "https://sqs.eu-west-1.amazonaws.com/503706440361/mobile-api-expense-reports-to-generate"}, {"name": "DATA_PLATFORM_API_BASE_URL", "value": "http://data-platform-api.destination.cluster.com:2830"}, {"name": "COMPETITIONS_API_BASE_URL", "value": "http://vpce-0f6cb243ead1814e0-exvodq7q.vpce-svc-0e829d50dd1ededde.eu-west-1.vpce.amazonaws.com"}, {"name": "CONNECTIVITY_STATUS_API_BASE_URL", "value": "http://vpce-065b6e5c48fd1c318-y45xyxlf.vpce-svc-09e9dc44df5f283ac.eu-west-1.vpce.amazonaws.com"}, {"name": "SMART_CHARGING_SERVICE_API_URL", "value": "http://vpce-03edfc3e51df9c516-jlypyf3m.vpce-svc-0098a39274b2a67e4.eu-west-1.vpce.amazonaws.com"}, {"name": "AUTH_SERVICE_USER_URL", "value": "http://vpce-0ec6af74d35e2c129-197nrud0.vpce-svc-07d873ae058848469.eu-west-1.vpce.amazonaws.com/api/v1/user"}, {"name": "GIP_AUTH_ISSUER_URL", "value": "https://www.googleapis.com/service_accounts/v1/jwk/<EMAIL>"}, {"name": "GIP_AUDIENCE", "value": "opencharge-mobile-app-stage"}, {"name": "API3_BASE_URL", "value": "http://vpce-0c0cdee059573bff8-mkj5otcr.vpce-svc-09e5237ef5f7d19a5.eu-west-1.vpce.amazonaws.com"}, {"name": "FIREBASE_PROJECT_ID", "value": "opencharge-mobile-app-stage"}, {"name": "FIREBASE_CLIENT_EMAIL", "value": "<EMAIL>"}, {"name": "DRIVER_ACCOUNT_API_BASE_URL", "value": "http://driver-account-api.destination.cluster.com:5104"}, {"name": "ASSETS_CONFIGURATION_API_BASE_URL", "value": "http://vpce-00f32a74cafd88f52-wbveuk7a.vpce-svc-0889284e5d729bbc8.eu-west-1.vpce.amazonaws.com"}, {"name": "ASSETS_SERVICE_API_BASE_URL", "value": "http://vpce-05ec802b62a4fdcd1-tuafvazl.vpce-svc-062c5544970b3c017.eu-west-1.vpce.amazonaws.com"}, {"name": "ASSETS_PROVISIONING_API_BASE_URL", "value": "http://vpce-03ad63fd6576ae9ae-vjuwxzi1.vpce-svc-09822256c018fc44f.eu-west-1.vpce.amazonaws.com"}, {"name": "IMAGES_BASE_URL", "value": "https://identity-stage.pod-point.com"}, {"name": "FIRMWARE_UPGRADE_BASE_URL", "value": "http://vpce-05bd4f7264633a46a-pvi421ys.vpce-svc-07d05f10917d25fa2.eu-west-1.vpce.amazonaws.com"}, {"name": "BILLING_API_BASE_URL", "value": "http://billing-api.destination.cluster.com:5108"}, {"name": "PAYMENTS_API_BASE_URL", "value": "http://payments-api.home-app.cluster.com:5121"}, {"name": "SUBSCRIPTIONS_API_BASE_URL", "value": "http://subscriptions-api.home-app.cluster.com:5120"}, {"name": "VEHICLE_API_BASE_URL", "value": "http://vpce-0e174aaa0295e431f-ljacyund.vpce-svc-074434b666abdbefb.eu-west-1.vpce.amazonaws.com"}, {"name": "ENODE_OAUTH_BASE_URL", "value": "https://oauth.sandbox.enode.io"}, {"name": "ENODE_API_BASE_URL", "value": "https://enode-api.sandbox.enode.io"}, {"name": "REMOTE_LOCK_UNSUPPORTED_CHARGERS", "value": "solo,solo3"}, {"name": "ENODE_VEHICLE_JSON_URL", "value": "https://cdn.pod-point.com/home-app/vehicles.json"}, {"name": "TARIFFS_API_URL", "value": "http://vpce-0c251fb8bd07a8209-lcscslyy.vpce-svc-0bd67c6d968d5d7b8.eu-west-1.vpce.amazonaws.com"}, {"name": "AXLE_API_BASE_URL", "value": "https://api.axle.energy"}, {"name": "AXLE_API_USERNAME", "value": "podpoint"}, {"name": "COHORTS_BASE_URL", "value": "https://cdn.pod-point.com/home-app/dev/cohorts"}, {"name": "ACQUIRED_API_BASE_URL", "value": "https://test-api.acquired.com"}, {"name": "POD_APP_MAGIC_LINK_URL", "value": "https://identity-stage.podenergy.com/pod/email-login"}, {"name": "LOYALTY_CARD_SERVICE_API_BASE_URL", "value": "http://api.loyalty-card-service.cluster.com:8102"}, {"name": "COP_TEST_MODE", "value": "true"}, {"name": "REWARDS_API_BASE_URL", "value": "http://rewards-api.home-app.cluster.com:5111"}], "essential": true, "image": "591651182110.dkr.ecr.eu-west-1.amazonaws.com/mobile-api:latest", "linuxParameters": {"initProcessEnabled": true}, "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/mobile-api", "awslogs-region": "eu-west-1", "awslogs-stream-prefix": "ecs"}}, "healthCheck": {"command": ["CMD-SHELL", "wget -qO - http://localhost:5102/health || exit 1"], "interval": 30, "timeout": 10, "retries": 5, "startPeriod": 120}, "mountPoints": [], "name": "mobile-api", "portMappings": [{"containerPort": 5102, "hostPort": 5102, "protocol": "tcp"}], "secrets": [{"name": "LOQATE_API_KEY", "valueFrom": "arn:aws:secretsmanager:eu-west-1:503706440361:secret:mobile-api-07wzTf:loqate_api_key::"}, {"name": "SENTRY_DSN", "valueFrom": "arn:aws:secretsmanager:eu-west-1:503706440361:secret:mobile-api-07wzTf:sentry_dsn::"}, {"name": "DRIVER_AUTH_ISSUER_URL", "valueFrom": "arn:aws:secretsmanager:eu-west-1:503706440361:secret:mobile-api-07wzTf:driver_auth_issuer_url::"}, {"name": "DRIVER_AUTH_AUDIENCE", "valueFrom": "arn:aws:secretsmanager:eu-west-1:503706440361:secret:mobile-api-07wzTf:driver_auth_audience::"}, {"name": "DRIVER_AUTH_JWKS", "valueFrom": "arn:aws:secretsmanager:eu-west-1:503706440361:secret:mobile-api-07wzTf:driver_auth_jwks::"}, {"name": "FIREBASE_PRIVATE_KEY", "valueFrom": "arn:aws:secretsmanager:eu-west-1:503706440361:secret:mobile-api-07wzTf:firebase_private_key::"}, {"name": "API3_CLIENT_ID", "valueFrom": "arn:aws:secretsmanager:eu-west-1:503706440361:secret:mobile-api-07wzTf:api3_client_id::"}, {"name": "API3_CLIENT_SECRET", "valueFrom": "arn:aws:secretsmanager:eu-west-1:503706440361:secret:mobile-api-07wzTf:api3_client_secret::"}, {"name": "DB_HOST", "valueFrom": "arn:aws:secretsmanager:eu-west-1:503706440361:secret:mobile-api-07wzTf:db_host::"}, {"name": "DB_HOST_RO", "valueFrom": "arn:aws:secretsmanager:eu-west-1:503706440361:secret:mobile-api-07wzTf:db_host_ro::"}, {"name": "DB_DATABASE", "valueFrom": "arn:aws:secretsmanager:eu-west-1:503706440361:secret:mobile-api-07wzTf:db_database::"}, {"name": "DB_PORT", "valueFrom": "arn:aws:secretsmanager:eu-west-1:503706440361:secret:mobile-api-07wzTf:db_port::"}, {"name": "DB_USERNAME", "valueFrom": "arn:aws:secretsmanager:eu-west-1:503706440361:secret:mobile-api-07wzTf:db_username::"}, {"name": "DB_PASSWORD", "valueFrom": "arn:aws:secretsmanager:eu-west-1:503706440361:secret:mobile-api-07wzTf:db_password::"}, {"name": "DRIVER_AUTH_PRIVATE_KEY", "valueFrom": "arn:aws:secretsmanager:eu-west-1:503706440361:secret:mobile-api-07wzTf:driver_auth_private_key::"}, {"name": "ENODE_CLIENT_ID", "valueFrom": "arn:aws:secretsmanager:eu-west-1:503706440361:secret:mobile-api-07wzTf:enode_client_id::"}, {"name": "ENODE_CLIENT_SECRET", "valueFrom": "arn:aws:secretsmanager:eu-west-1:503706440361:secret:mobile-api-07wzTf:enode_client_secret::"}, {"name": "CUSTOM_API_TOKEN", "valueFrom": "arn:aws:secretsmanager:eu-west-1:503706440361:secret:mobile-api-07wzTf:custom_api_token::"}, {"name": "AXLE_API_PASSWORD", "valueFrom": "arn:aws:secretsmanager:eu-west-1:503706440361:secret:mobile-api-07wzTf:axle_api_password::"}, {"name": "ACQUIRED_API_APP_ID", "valueFrom": "arn:aws:secretsmanager:eu-west-1:503706440361:secret:mobile-api-07wzTf:acquired_api_app_id::"}, {"name": "ACQUIRED_API_APP_KEY", "valueFrom": "arn:aws:secretsmanager:eu-west-1:503706440361:secret:mobile-api-07wzTf:acquired_api_app_key::"}], "volumesFrom": []}], "cpu": "512", "executionRoleArn": "arn:aws:iam::503706440361:role/mobile-api-ecs-task-execution", "family": "mobile-api", "memory": "1024", "networkMode": "awsvpc", "placementConstraints": [], "requiresCompatibilities": ["FARGATE"], "taskRoleArn": "arn:aws:iam::503706440361:role/mobile-api-ecs-task", "volumes": []}