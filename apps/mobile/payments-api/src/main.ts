import * as Sentry from '@sentry/node';
import { AppModule } from './app/app.module';
import {
  beforeSendSentryError,
  bootstrap,
} from '@experience/shared/nest/utils';
import { getSwaggerDocs } from './swagger/swagger';
import axios from 'axios';

Sentry.init({
  dsn: process.env.SENTRY_DSN,
  enabled: process.env.ENVIRONMENT
    ? ['dev', 'stage', 'prod'].includes(process.env.ENVIRONMENT)
    : false,
  environment: process.env.ENVIRONMENT,
  ignoreTransactions: ['/health'],
  release: process.env.SENTRY_RELEASE,
  tracesSampleRate: 0.001,
  beforeSend: (event, hint) => beforeSendSentryError(event, hint, []),
});

axios.defaults.headers.common['User-Agent'] = 'mobile/payments-api';

void bootstrap({
  module: AppModule,
  port: 5121,
  rawBody: true,
  callback: (app, documentBuilder) => getSwaggerDocs(documentBuilder),
});
