import { ApiProperty } from '@nestjs/swagger';
import {
  AppointmentBuilder,
  AppointmentPayload,
  AppointmentReferencePayload,
} from '@experience/installer/types';
import {
  ChargerSettings,
  Install,
  NetworkInterface,
  NetworkState,
  PowerBalancingSensor,
  SocketType,
} from '../database/entities/install.entity';
import { InstallImageResponse } from '../install-images/install-image.types';
import { InstallerType, UserProfile } from '../account/account.types';
import {
  IsEnum,
  IsNotEmpty,
  IsOptional,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

const anExampleChargerSettingForApi: ChargerSettings = {
  connectivity: {
    wifiConnectedState: 'Connected',
    signalStrength: 0,
    networkState: NetworkState.Disconnected,
    networkInterface: NetworkInterface.Wifi,
  },

  deviceInformation: {
    firmware: 'v1.X.X.X',
    pcbSerialNumber: '123456789x',
    pslNumber: 'PSL1234',
    unitSKU: 'S7-2C-FAF',
    wifiMacAddress: '00-B0-D0-63-C2-26',
    socket: SocketType.A,
  },
  deviceConfig: {
    breakerSize: 100,
    powerGenerationSystemInstalled: false,
    powerBalancingSensorInstalled: false,
    powerBalancingEnabled: false,
    householdMaxSupply: 32,
    powerRatingPerPhase: 50,
    outOfService: true,
    linkySchedulesEnabled: false,
    powerBalancingSensor: PowerBalancingSensor.NONE,
  },

  sensorReadings: {
    powerBalancingCt: 3.212,
  },
};

const exampleUserProfile: UserProfile = {
  authId: '12345',
  companyName: 'Electrical Services Ltd.',
  companyNumber: 'SC327000',
  companyType: 'company',
  email: '<EMAIL>',
  firstName: 'Joe',
  lastName: 'Spark',
  marketingConsent: true,
  phoneNumber: '+44 ************',
  installerType: InstallerType.THIRD_PARTY,
};

class DeviceInformation {
  @ApiProperty()
  unitSKU: string;
  @ApiProperty()
  pcbSerialNumber: string;
  @ApiProperty()
  firmware: string;
  @ApiProperty()
  pslNumber: string;
  @ApiProperty()
  wifiMacAddress: string;
  @ApiProperty({
    description:
      'A or B respectively for left or right side of twin or null for single unit',
    example: 'A',
    enum: SocketType,
    nullable: true,
    required: false,
  })
  @IsEnum(SocketType)
  @IsOptional()
  socket?: SocketType;
}

class Connectivity {
  @ApiProperty()
  wifiConnectedState: string;
  @ApiProperty()
  signalStrength: number;
  @ApiProperty({ enum: NetworkState })
  @IsEnum(NetworkState)
  networkState: NetworkState;
  @ApiProperty({ enum: NetworkInterface, required: false })
  @IsEnum(NetworkInterface)
  @IsOptional()
  networkInterface?: NetworkInterface;
}

class DeviceConfig {
  @ApiProperty()
  powerGenerationSystemInstalled: boolean;
  @ApiProperty()
  breakerSize: number;
  @ApiProperty()
  powerBalancingSensorInstalled: boolean;
  @ApiProperty()
  powerBalancingEnabled: boolean;
  @ApiProperty()
  householdMaxSupply: number;
  @ApiProperty()
  powerRatingPerPhase: number;
  @ApiProperty()
  outOfService: boolean;
  @ApiProperty({
    required: false,
  })
  linkySchedulesEnabled?: boolean;
  @IsEnum(PowerBalancingSensor)
  @ApiProperty({
    example: 'NONE',
    enum: PowerBalancingSensor,
    required: false,
  })
  powerBalancingSensor?: PowerBalancingSensor;
}

class SensorReadings {
  @ApiProperty()
  powerBalancingCt: number;
}

export class ChargerSettingsPayload implements ChargerSettings {
  @ApiProperty({ type: DeviceInformation })
  @ValidateNested()
  @Type(() => DeviceInformation)
  deviceInformation: DeviceInformation;
  @ApiProperty({ type: DeviceConfig, required: false })
  deviceConfig?: DeviceConfig;
  @ApiProperty({ type: SensorReadings, required: false })
  sensorReadings?: SensorReadings;
  @ApiProperty({ type: Connectivity, required: false })
  @ValidateNested()
  @Type(() => Connectivity)
  connectivity?: Connectivity;
}

export class InstallSession {
  @ApiProperty({
    description: 'Charger settings JSON object',
    example: anExampleChargerSettingForApi,
  })
  @ValidateNested()
  @Type(() => ChargerSettingsPayload)
  chargerSettings: ChargerSettingsPayload;

  @ApiProperty({
    description: 'Appointment payload',
    required: false,
  })
  @ValidateNested()
  @Type(() => AppointmentPayload)
  appointment?: AppointmentPayload;

  @ApiProperty({
    description: 'Completion date in ISO8601',
    example: '2023-07-07T09:06:07Z',
    nullable: true,
    required: false,
  })
  completedAt?: string;

  @ApiProperty({
    description: 'Google Identity Platform user id',
    type: String,
    example: 'f41e4a67-3aea-43bd-a8dc-12fdeb9524af',
  })
  @IsNotEmpty({ message: 'The authId field is required' })
  authId: string;
}

export class UpsertInstallResponse {
  @ApiProperty({
    description: 'first completed at date',
    example: '2023-07-07T09:06:07Z',
    required: false,
    nullable: true,
  })
  firstCompletedAt?: string;
}

const exampleImage: InstallImageResponse = {
  id: 1,
  installGuid: 'e6dfc67b-7df8-42ad-98fb-eab240202d05',
  file: '991a238-dbac-4ea8-a9cd-1eb2444294c3/2361d2fe-8eaa-4323-a34a-76b9e229afcc.jpeg',
  url: 'https://installer-api-images.s3.eu-west-1.amazonaws.com/991a238-dbac-4ea8-a9cd-1eb2444294c3/2361d2fe-8eaa-4323-a34a-76b9e229afcc.jpeg',
  label: 'front',
  createdAt: '2024-04-30T13:15:01.445Z',
  updatedAt: '2024-04-30T13:15:01.445Z',
};

type InstallResponseType = Omit<Install, 'user' | 'images' | 'appointment'> & {
  images?: InstallImageResponse[];
  appointment: AppointmentReferencePayload | null;
};

export class InstallResponse implements InstallResponseType {
  @ApiProperty({
    description: 'PSL number',
    example: 'PSL-12345',
  })
  pslNumber: string;

  @ApiProperty({
    description: 'Charger settings JSON object',
    example: anExampleChargerSettingForApi,
  })
  chargerSettings: ChargerSettingsPayload;

  @ApiProperty({
    description: 'Appointment payload',
    nullable: true,
    example: {
      reference: new AppointmentBuilder().build().reference,
    },
  })
  appointment: AppointmentReferencePayload | null;

  @ApiProperty({
    description: 'Completion date in ISO8601',
    example: '2023-07-07T09:06:07Z',
    nullable: true,
  })
  completedAt?: Date;

  @ApiProperty({
    description: 'Created date in ISO8601',
    example: '2023-07-07T09:06:07Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'GUID of the install',
    example: '1308b396-55c5-428f-8fd3-be78f3643350',
  })
  guid: string;

  @ApiProperty({
    description: 'The numerical ID of the install',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'The images associated with the install',
    example: [exampleImage],
    type: [InstallImageResponse],
  })
  images?: InstallImageResponse[];

  @ApiProperty({
    description:
      'A or B respectively for left or right side of twin or null for single unit',
    example: 'A',
    enum: SocketType,
    nullable: true,
    required: false,
  })
  @IsEnum(SocketType)
  @IsOptional()
  socket?: SocketType;

  @ApiProperty({
    description: 'Updated date in ISO8601',
    example: '2023-07-07T09:06:07Z',
  })
  updatedAt: Date;

  @ApiProperty({
    description: 'The user profile of the installer',
    example: exampleUserProfile,
    type: UserProfile,
  })
  user: UserProfile;
}
