import {
  EntityParams,
  ParanoidEntityInterface,
  UnpersistedEntityHelper,
} from '@experience/mobile/clean-architecture';
import { OrderEntity, UnpersistedOrderEntity } from './order.entity';
import { PlanEntity, UnpersistedPlanEntity } from './plan.entity';

export enum SubscriptionStatus {
  PENDING = 'PENDING',
  ACTIVE = 'ACTIVE',
  CANCELLED = 'CANCELLED',
  SUSPENDED = 'SUSPENDED',
  ENDED = 'ENDED',
  REJECTED = 'REJECTED',
}

export class SubscriptionEntity implements ParanoidEntityInterface<string> {
  constructor(params: EntityParams<SubscriptionEntity>) {
    Object.assign(this, params);
  }

  id: string;
  userId: string; // firebase user id
  status: SubscriptionStatus;
  // TODO: this probably wants to removed from this entity
  order: OrderEntity;
  plan: PlanEntity;
  activatedAt: Date | null;
  createdAt: Date;
  updatedAt: Date;
  deletedAt: Date | null;
}

export class UnpersistedSubscriptionEntity
  implements UnpersistedEntityHelper<SubscriptionEntity>
{
  constructor(params: EntityParams<UnpersistedSubscriptionEntity>) {
    Object.assign(this, params);
  }

  userId: string;
  status: SubscriptionStatus;
  plan: UnpersistedPlanEntity;
  order: UnpersistedOrderEntity;
  activatedAt: Date | null;
}
