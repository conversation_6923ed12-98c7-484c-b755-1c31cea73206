import { AuthActionsController } from './auth.actions.controller';
import { AuthActionsService } from './auth.actions.service';
import { AuthApi, Configuration } from '@experience/installer/api/axios';
import { AuthorisationModule } from '../authorisation/authorisation.module';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { Module } from '@nestjs/common';

@Module({
  imports: [AuthorisationModule, ConfigModule],
  controllers: [AuthActionsController],
  providers: [
    AuthActionsService,
    {
      inject: [ConfigService],
      provide: AuthApi,
      useFactory: async (configService: ConfigService) =>
        new AuthApi(
          new Configuration({
            basePath: configService.get('INSTALLER_API_BASE_URL'),
          })
        ),
    },
  ],
})
export class AuthActionsModule {}
